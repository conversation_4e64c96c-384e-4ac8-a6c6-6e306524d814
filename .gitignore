# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# Dependencies
/node_modules
/.pnp
.pnp.js
/npm-debug.log*
/yarn-debug.log*
/yarn-error.log*
/lerna-debug.log*
.npm
.eslintcache

# Testing
/coverage
*.lcov
/nyc_output
/.nyc_output

# Next.js
/.next/
/out/
/build
/.swc/

# Production builds
/dist
/build

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
.env*.local

# API Keys (security)
.env.example
config/keys.js
config/keys.json

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*
.pnpm-debug.log*

# Diagnostic reports
report.[0-9]*.[0-9]*.[0-9]*.[0-9]*.json

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional stylelint cache
.stylelintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# Next.js build output
.next
out

# Nuxt.js build / generate output
.nuxt
dist

# Gatsby files
.cache/
public

# Storybook build outputs
.out
.storybook-out
storybook-static

# Temporary folders
tmp/
temp/

# Editor directories and files
.vscode/*
!.vscode/extensions.json
.idea
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# TypeScript
*.tsbuildinfo
next-env.d.ts
*.d.ts.map

# Vercel
.vercel

# Turbo
.turbo

# Sentry
.sentryclirc

# Database
*.db
*.sqlite
*.sqlite3

# Certificates
*.pem
*.key
*.crt

# Local development
.local

# PWA files
**/public/sw.js
**/public/workbox-*.js
**/public/worker-*.js
**/public/sw.js.map
**/public/workbox-*.js.map
**/public/worker-*.js.map

# Backup files
*.bak
*.backup
*.old

# Lock files (choose one based on your package manager)
# package-lock.json  # Uncomment if using yarn
# yarn.lock          # Uncomment if using npm
# pnpm-lock.yaml     # Uncomment if using yarn/npm
